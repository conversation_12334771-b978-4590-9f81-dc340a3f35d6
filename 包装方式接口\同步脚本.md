```javascript
//定义返回数据数组
var datalist = [];
//主函数
var TopRowCount = 0;//设置每次程序执行抓取的总数，0为全部
function main() {

    var StartRow = 0;//设置初始开始查询位置
    var Limit = 5000;//设置递归每页抓取条数
    var FieldKeys_str = "";
    //SsIoTblfList拿到的是字段映射列表
    for (var i = 0; i < SsIoTblfList.length; i++) {
        FieldKeys_str = FieldKeys_str + SsIoTblfList[i]["io_field_source"] + ","
    }
    //设置查询哪些字段
    FieldKeys_str = FieldKeys_str.slice(0, -1);
    var FieldKeys = FieldKeys_str.split(',');
    getDataList(StartRow, Limit, FieldKeys_str);
    Logger.LogInformation("抓取到" + datalist.length + "条记录");//写日志
    var res_data = [];
    for (var i = 0; i < datalist.length; i++) {
        var item = {};
        for (var k = 0; k < FieldKeys.length; k++) {
            item[FieldKeys[k]] = datalist[i][k];

        }
        res_data.push(item);
    }

    const result = res_data.filter((item, index, self) => {
        return self.findIndex(t => t.FNumber=== item.FNumber) === index;
    });
    Logger.LogInformation("去重后还剩" + result.length + "条记录");//写日志
    return result;
}
//递归函数分页拉取金蝶数据
function getDataList(StartRow, Limit, FieldKeys) {
    //获取授权动作中返回的cookie
    var cookies = SsIoReadAuth.ext.cookie;
    Logger.LogInformation("cookies" + cookies);//写日志
    var handler = { Cookie: cookies };//
    var httpClientHandler = new HttpClientHandler();
    var client = new HttpClient(httpClientHandler);
     Logger.LogInformation(SsIoTbl.io_last_data_time.ToString("yyyy-MM-dd HH:mm:ss.ffffff"));
    var formData = "data=" + JSON.stringify({
        FormId: "BOS_ASSISTANTDATA_DETAIL",
        FieldKeys: FieldKeys,
        //"FilterString": [{"Left":"(","FieldName":"FID","Compare":"=","Value":"59cfc05ac17916","Right":")","Logic":"AND"}],
        OrderString: "",
        TopRowCount: TopRowCount,
        StartRow: StartRow,
        Limit: Limit,
        SubSystemId: ""
    });

    var content = new StringContent(
        formData,
        Encoding.UTF8,
        "application/x-www-form-urlencoded"
    );
    client.DefaultRequestHeaders.Add("Cookie", cookies);
    var response = client.PostAsync(SsIoReadAuth.io_auth_db_host + "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc", content).Result;
    response.EnsureSuccessStatusCode();
    var responseBody = response.Content.ReadAsStringAsync().Result;
    Logger.LogInformation("读取数据结果:");
    Logger.LogInformation(responseBody);
    var result = JSON.parse(responseBody);

    if (result["FSrcBillNo"] != null && result["FSrcBillNo"]["Result"]["ResponseStatus"]["Errors"].length > 0) {
        throw response;
    }
    if (result[0] != null && result[0][0] != null && result[0][0]["Result"] != null) {
        throw response;
    }

    //将每次的数据与datalist合并   
    if (result.length > 0) {
        datalist = datalist.concat(result);
    }

    if (result.length >= Limit) {
        StartRow = StartRow + Limit;
        getDataList(StartRow, Limit, FieldKeys);
    }
}