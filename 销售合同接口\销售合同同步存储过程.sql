

CREATE OR REPLACE FUNCTION public.af_cr_sf_h_temp_sync_data()
  RETURNS "pg_catalog"."varchar" AS $BODY$
    -- 销售合同数据同步：从临时表同步到正式表
    -- 销售合同数据同步存储过程
    -- 功能：将 cr_sf_h_temp 表中的数据同步到 cr_sf_h 表和 cr_sf_b 表中
declare 
    res returntype;
    sync_count integer := 0;
    error_count integer := 0;
    current_sf_h_id text;
    temp_record record;
begin
    
    -- 开始事务处理
    BEGIN
        
        -- 1. 同步表头数据到 cr_sf_h 表
        INSERT INTO cr_sf_h(
            sf_h_id,
            sf_h_status, 
            sf_h_no,
            company_no,
            company_name,
            client_no,
            client_name,
            sf_h_rcv_datetime,
            cr_sf_h_rmk01,
            cr_sf_h_rmk02,
            cr_sf_h_rmk03,
            cr_sf_h_rmk04,
            cr_sf_h_rmk05,
            cr_sf_h_rmk06,
            crt_time,
            crt_user,
            crt_user_no,
            crt_user_name,
            crt_host,
            upd_time,
            upd_user,
            upd_user_no,
            upd_user_name,
            upd_host
        )
        SELECT DISTINCT
            COALESCE(temp.sf_h_id, af_auid()) as sf_h_id,
            COALESCE(temp.sf_h_status, '0') as sf_h_status,
            COALESCE(temp.sf_h_no, af_ss_no_generate('sf_h_no'::character varying)) as sf_h_no,
            temp.company_no,
            temp.company_name,
            temp.client_no,
            temp.client_name,
            COALESCE(temp.sf_h_rcv_datetime, now()::date) as sf_h_rcv_datetime,
            temp.cr_sf_h_rmk01,
            temp.cr_sf_h_rmk02,
            temp.cr_sf_h_rmk03,
            temp.cr_sf_h_rmk04,
            temp.cr_sf_h_rmk05,
            temp.cr_sf_h_rmk06,
            COALESCE(temp.crt_time, now()) as crt_time,
            COALESCE(temp.crt_user, 'sys') as crt_user,
            COALESCE(temp.crt_user_no, 'sys') as crt_user_no,
            COALESCE(temp.crt_user_name, 'sys') as crt_user_name,
            COALESCE(temp.crt_host, 'sys') as crt_host,
            COALESCE(temp.upd_time, now()) as upd_time,
            COALESCE(temp.upd_user, 'sys') as upd_user,
            COALESCE(temp.upd_user_no, 'sys') as upd_user_no,
            COALESCE(temp.upd_user_name, 'sys') as upd_user_name,
            COALESCE(temp.upd_host, 'sys') as upd_host
        FROM cr_sf_h_temp temp
        WHERE temp.company_no IS NOT NULL 
          AND temp.client_no IS NOT NULL
          AND temp.part_no IS NOT NULL  -- 确保有明细数据
        ON CONFLICT (sf_h_no) DO UPDATE
        SET 
            sf_h_status = EXCLUDED.sf_h_status,
            company_no = EXCLUDED.company_no,
            company_name = EXCLUDED.company_name,
            client_no = EXCLUDED.client_no,
            client_name = EXCLUDED.client_name,
            sf_h_rcv_datetime = EXCLUDED.sf_h_rcv_datetime,
            cr_sf_h_rmk01 = EXCLUDED.cr_sf_h_rmk01,
            cr_sf_h_rmk02 = EXCLUDED.cr_sf_h_rmk02,
            cr_sf_h_rmk03 = EXCLUDED.cr_sf_h_rmk03,
            cr_sf_h_rmk04 = EXCLUDED.cr_sf_h_rmk04,
            cr_sf_h_rmk05 = EXCLUDED.cr_sf_h_rmk05,
            cr_sf_h_rmk06 = EXCLUDED.cr_sf_h_rmk06,
            upd_time = EXCLUDED.upd_time,
            upd_user = EXCLUDED.upd_user,
            upd_user_no = EXCLUDED.upd_user_no,
            upd_user_name = EXCLUDED.upd_user_name,
            upd_host = EXCLUDED.upd_host;

        -- 获取同步的表头记录数
        GET DIAGNOSTICS sync_count = ROW_COUNT;
        
        -- 2. 同步明细数据到 cr_sf_b 表
        INSERT INTO cr_sf_b(
            sf_b_id,
            sf_h_id,
            part_no,
            part_name,
            part_spec,
            part_idt,
            sf_date,
            sf_qty,
            cr_sf_b_rmk01,
            cr_sf_b_rmk02,
            cr_sf_b_rmk03,
            cr_sf_b_rmk04,
            crt_time,
            crt_user,
            crt_user_no,
            crt_user_name,
            crt_host,
            upd_time,
            upd_user,
            upd_user_no,
            upd_user_name,
            upd_host,
            sf_b_status,
            mrp_region_no,
            part_length,
            part_width,
            part_height,
            rmh_version,
            segment_length
        )
        SELECT 
            af_auid() as sf_b_id,
            h.sf_h_id,  -- 关联正式表头ID
            temp.part_no,
            temp.part_name,
            temp.part_spec,
            temp.part_no as part_idt,  -- 使用品号作为产品规格码
            COALESCE(temp.sf_date, now()::date) as sf_date,
            COALESCE(temp.sf_qty, 0) as sf_qty,
            temp.cr_sf_b_rmk01,
            temp.cr_sf_h_rmk02 as cr_sf_b_rmk02,  -- 可以从表头备注映射
            temp.cr_sf_h_rmk03 as cr_sf_b_rmk03,
            temp.cr_sf_h_rmk04 as cr_sf_b_rmk04,
            COALESCE(temp.crt_time, now()) as crt_time,
            COALESCE(temp.crt_user, 'sys') as crt_user,
            COALESCE(temp.crt_user_no, 'sys') as crt_user_no,
            COALESCE(temp.crt_user_name, 'sys') as crt_user_name,
            COALESCE(temp.crt_host, 'sys') as crt_host,
            COALESCE(temp.upd_time, now()) as upd_time,
            COALESCE(temp.upd_user, 'sys') as upd_user,
            COALESCE(temp.upd_user_no, 'sys') as upd_user_no,
            COALESCE(temp.upd_user_name, 'sys') as upd_user_name,
            COALESCE(temp.upd_host, 'sys') as upd_host,
            COALESCE(temp.sf_b_status, '310') as sf_b_status,
            temp.mrp_region_no,
            COALESCE(temp.part_length, 0) as part_length,
            COALESCE(temp.part_width, 0) as part_width,
            COALESCE(temp.part_height, 0) as part_height,
            temp.rmh_version,
            COALESCE(temp.segment_length, 99999999) as segment_length
        FROM cr_sf_h_temp temp
        INNER JOIN cr_sf_h h ON h.sf_h_no = temp.sf_h_no  -- 关联已同步的表头
        WHERE temp.part_no IS NOT NULL
          AND temp.part_name IS NOT NULL
          AND NOT EXISTS (
              -- 避免重复插入相同的明细记录
              SELECT 1 FROM cr_sf_b b 
              WHERE b.sf_h_id = h.sf_h_id 
                AND b.part_no = temp.part_no 
                AND b.sf_date = COALESCE(temp.sf_date, now()::date)
          );

        -- 3. 清空临时表数据（可选，根据业务需求决定）
        -- DELETE FROM cr_sf_h_temp WHERE sf_h_no IN (
        --     SELECT sf_h_no FROM cr_sf_h 
        --     WHERE sf_h_no IS NOT NULL
        -- );
        
        -- 或者使用 TRUNCATE（清空整个临时表）
        -- TRUNCATE cr_sf_h_temp;
        
        res := row('true', '数据同步成功！同步表头记录数：' || sync_count);
        return to_json(res);
        
    EXCEPTION 
        WHEN OTHERS THEN
            -- 发生错误时回滚事务
            ROLLBACK;
            res := row('false', '数据同步失败：' || SQLERRM);
            return to_json(res);
    END;

end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;
