# 数据源字段与 cr_part_pack_temp 表字段映射文档

## 概述
本文档描述了数据源（FBillHead 辅助资料）字段与目标表 `cr_part_pack_temp` 字段之间的映射关系。

## 字段映射表

### 直接映射字段

| 数据源字段 | 目标表字段 | 字段类型 | 说明 | 映射关系 |
|-----------|-----------|---------|------|---------|
| FNumber | pack_no | text | 编码 → 零件编号 | 直接映射 |
| FDataValue | pack_name | text | 名称 → 包装类型 | 需要转换为包装类型代码 |
| FDescription | cr_part_pack_rmk01 | text | 备注 → 备注字段1 | 直接映射 |
| FSeq | - | - | 显示顺序 | 无对应字段 |
| FCreatorId | crt_user | text | 创建人 → 创建用户 | 直接映射 |
| FCreateDate | crt_time | timestamp | 创建日期 → 创建时间 | 直接映射 |
| FModifierId | upd_user | text | 修改人 → 更新用户 | 直接映射 |
| FModifyDate | upd_time | timestamp | 修改日期 → 更新时间 | 直接映射 |

### 需要转换的字段

| 数据源字段 | 目标表字段 | 转换规则 | 说明 |
|-----------|-----------|---------|------|
| FDataValue | pack_type | 名称转代码映射 | 需要建立包装类型名称到代码的映射表 |
| FId | client_no | 类别转客户编号 | 需要根据业务规则转换 |

### 目标表特有字段（无数据源对应）

| 目标表字段 | 字段类型 | 默认值/处理方式 | 说明 |
|-----------|---------|----------------|------|
| cr_part_pack_id | text | af_auid() | 主键，系统自动生成 |
| client_no | text | 需要业务规则确定 | 客户编号，必填 |
| part_qty | numeric(18,4) | 1 | 零件数量，默认为1 |
| parent_pack_type | text | NULL | 父包装类型 |
| pack_length | int4 | 0 | 包装长度 |
| pack_width | int4 | 0 | 包装宽度 |
| pack_height | int4 | 0 | 包装高度 |
| pack_weight_gross | numeric(18,4) | 0 | 毛重 |
| pack_weight_net | numeric(18,4) | 0 | 净重 |
| ischeck_parent_pack_type | bool | NULL | 是否检查父包装类型 |
| is_pack_test_ok | bool | NULL | 包装测试是否通过 |
| pack_rule | text | NULL | 包装规则（1-6） |
| cr_part_pack_rmk02~rmk10 | text | NULL | 备注字段2-10 |
| crt_user_no | text | NULL | 创建用户编号 |
| crt_user_name | text | NULL | 创建用户姓名 |
| crt_host | text | 需要获取 | 创建主机 |
| upd_user_no | text | NULL | 更新用户编号 |
| upd_user_name | text | NULL | 更新用户姓名 |
| upd_host | text | 需要获取 | 更新主机 |

### 数据源特有字段（目标表无对应）

| 数据源字段 | 说明 | 处理方式 |
|-----------|------|---------|
| FEntryID | 实体主键 | 不映射 |
| FIsSysPreset | 系统预置 | 不映射 |
| FCreateOrgId | 创建组织 | 不映射 |
| FUseOrgId | 使用组织 | 不映射 |
| FForbidDate | 禁用日期 | 不映射 |
| FForbiderId | 禁用人 | 不映射 |
| FForbidStatus | 禁用状态 | 不映射 |
| FDocumentStatus | 数据状态 | 不映射 |
| FApproverID | 审核人 | 不映射 |
| FApproveDate | 审核日期 | 不映射 |
| FParentId | 上级资料 | 不映射 |
| F_BHS_ProjectType | 项目类型 | 不映射 |
| F_BHS_ProjectLeader | 项目负责人 | 不映射 |
| F_BHS_ApplicationOrg | 申请组织 | 不映射 |
| F_BHS_ApplicationDep | 申请部门 | 不映射 |
| F_BHS_ApplicationPer | 申请人 | 不映射 |
| F_BHS_ApplicationPhone | 申请人电话 | 不映射 |
| F_BHS_Budget | 项目预算 | 不映射 |
| F_BHS_ProjectDep | 项目部门 | 不映射 |
| F_BHS_CostOrg | 费用承担组织 | 不映射 |

## 数据转换规则

### 包装类型代码映射
根据表约束，pack_type 字段只能是以下值之一：
- '00': 待定义
- '10': 待定义  
- '30': 待定义
- '50': 待定义
- '70': 待定义

需要建立 FDataValue（名称）到这些代码的映射关系。

### 包装规则代码
pack_rule 字段只能是以下值之一：
- '1': 待定义
- '2': 待定义
- '3': 待定义
- '4': 待定义
- '5': 待定义
- '6': 待定义

## 数据迁移建议

1. **必填字段处理**：确保 client_no、part_no、pack_type 字段有有效值
2. **数据验证**：迁移前验证数据符合表约束条件
3. **默认值设置**：为没有数据源对应的字段设置合理的默认值
4. **数据清洗**：处理数据源中的空值和异常值
5. **映射表建立**：建立包装类型名称到代码的映射表

## 注意事项

1. 目标表有外键约束，需要确保 client_no 和 part_no 在相关表中存在
2. 包装类型和包装规则有严格的值约束，需要预先定义映射规则
3. 时间戳字段需要注意时区转换
4. 备注字段可以考虑将数据源的多个字段合并到目标表的多个备注字段中
