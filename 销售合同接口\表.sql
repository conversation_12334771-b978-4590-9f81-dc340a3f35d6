CREATE TABLE public.cr_sf_h (
	sf_h_id text DEFAULT af_auid() NOT NULL,
	sf_h_status text DEFAULT '0'::text NOT NULL,
	sf_h_no text DEFAULT af_ss_no_generate('sf_h_no'::character varying) NOT NULL,
	company_no text NOT NULL,
	company_name text NULL,
	client_no text NOT NULL,
	client_name text NULL,
	sf_h_rcv_datetime date DEFAULT now() NOT NULL,
	cr_sf_h_rmk01 text NULL,
	cr_sf_h_rmk02 text NULL,
	cr_sf_h_rmk03 text NULL,
	cr_sf_h_rmk04 text NULL,
	cr_sf_h_rmk05 text NULL,
	cr_sf_h_rmk06 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	CONSTRAINT cr_sf_h_check CHECK ((sf_h_status = ANY (ARRAY['0'::text, '1'::text]))),
	CONSTRAINT cr_sf_h_un UNIQUE (sf_h_no),
	CONSTRAINT pk_cr_sf_h PRIMARY KEY (sf_h_id),
	CONSTRAINT cr_sf_h_fk FOREIGN KEY (client_no) REFERENCES public.cr_client(client_no) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT cr_sf_h_fk2 FOREIGN KEY (company_no) REFERENCES public.ss_company(company_no) ON DELETE RESTRICT ON UPDATE CASCADE
)
WITH (
	autovacuum_enabled=true
);
CREATE UNIQUE INDEX cr_sf_h_sf_h_no_idx ON public.cr_sf_h USING btree (sf_h_no);
CREATE INDEX cr_sf_h_sf_h_status_idx ON public.cr_sf_h USING btree (sf_h_status);
CREATE INDEX ix_cr_sf_h2 ON public.cr_sf_h USING btree (client_no);

-- Table Triggers

create trigger af_cr_sf_h_tri_after after
insert
    or
update
    of sf_h_status,
    company_no,
    client_no on
    public.cr_sf_h for each row execute function af_cr_sf_h_trigger();





CREATE TABLE public.cr_sf_b (
	sf_b_id text DEFAULT af_auid() NOT NULL,
	sf_h_id text NOT NULL,
	part_no text NOT NULL,
	part_name text NOT NULL,
	part_spec text NULL,
	part_idt text NULL,
	sf_date date DEFAULT now() NOT NULL,
	sf_qty numeric(18, 3) DEFAULT 0 NOT NULL,
	cr_sf_b_rmk01 text NULL,
	cr_sf_b_rmk02 text NULL,
	cr_sf_b_rmk03 text NULL,
	cr_sf_b_rmk04 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	sf_b_status text DEFAULT '310'::text NOT NULL,
	mrp_region_no text NULL,
	part_length numeric(18, 3) DEFAULT 0 NOT NULL,
	part_width numeric(18, 3) DEFAULT 0 NOT NULL,
	part_height numeric(18, 3) DEFAULT 0 NOT NULL,
	rmh_version text NULL,
	segment_length int4 DEFAULT 99999999 NOT NULL,
	CONSTRAINT pk_cr_sf_b PRIMARY KEY (sf_b_id),
	CONSTRAINT cr_sf_b_cr_sf_h_fk FOREIGN KEY (sf_h_id) REFERENCES public.cr_sf_h(sf_h_id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT cr_sf_b_fk2 FOREIGN KEY (part_no) REFERENCES public.pd_part(part_no) ON DELETE RESTRICT ON UPDATE CASCADE
)
WITH (
	autovacuum_enabled=true
);
CREATE INDEX cr_sf_b_sf_b_status_idx ON public.cr_sf_b USING btree (sf_b_status);





CREATE TABLE public.cr_sf_h_temp (
	sf_h_id text DEFAULT af_auid() NOT NULL,
	sf_h_status text DEFAULT '0'::text NULL,
	sf_h_no text DEFAULT af_ss_no_generate('sf_h_no'::character varying) NULL,
	company_no text NULL,
	company_name text NULL,
	client_no text NULL,
	client_name text NULL,
	sf_h_rcv_datetime date DEFAULT now() NULL,
	cr_sf_h_rmk01 text NULL,
	cr_sf_h_rmk02 text NULL,
	cr_sf_h_rmk03 text NULL,
	cr_sf_h_rmk04 text NULL,
	cr_sf_h_rmk05 text NULL,
	cr_sf_h_rmk06 text NULL,
	crt_time timestamp(6) NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp(6) NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	sf_date date NULL,
	sf_qty numeric(18, 3) NULL,
	sf_b_status text NULL,
	mrp_region_no text NULL,
	part_length numeric(18, 3) NULL,
	part_width numeric(18, 3) NULL,
	part_height numeric(18, 3) NULL,
	rmh_version text NULL,
	segment_length int4 NULL,
	cr_sf_b_rmk01 text NULL,
	as_plan_start_datetime text NULL,
	as_plan_end_datetime text NULL,
	CONSTRAINT cr_sf_h_copy1_pkey PRIMARY KEY (sf_h_id),
	CONSTRAINT cr_sf_h_copy1_sf_h_no_key UNIQUE (sf_h_no)
);

-- Table Triggers

create trigger af_cr_sf_h_tri_after after
insert
    or
update
    of sf_h_status,
    company_no,
    client_no on
    public.cr_sf_h_temp for each row execute function af_cr_sf_h_trigger();
