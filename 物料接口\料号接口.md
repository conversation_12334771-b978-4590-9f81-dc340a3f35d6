表单ID：BD_MATERIAL（db_material）





## 一、请求参数说明：

1.formid：业务对象表单Id，字符串类型（必录）
2.data：JSON格式数据（详情参考JSON格式数据）（必录）
     2.1.NeedUpDateFields：需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
     2.2.NeedReturnFields：需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
     2.3.IsDeleteEntry：是否删除已存在的分录，布尔类型，默认true（非必录）
     2.4.SubSystemId：表单所在的子系统内码，字符串类型（非必录）
     2.5.IsVerifyBaseDataField：是否验证所有的基础资料有效性，布尔类，默认false（非必录）
     2.6.IsEntryBatchFill：是否批量填充分录，默认true（非必录）
     2.7.ValidateFlag：是否验证标志，布尔类型，默认true（非必录）
     2.8.NumberSearch：是否用编码搜索基础资料，布尔类型，默认true（非必录）
     2.9.IsAutoAdjustField：是否自动调整JSON字段顺序，布尔类型，默认false（非必录）
     2.10.InterationFlags：交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
     2.11.IgnoreInterationFlag：是否允许忽略交互，布尔类型，默认true（非必录）
     2.12.Model：表单数据包，JSON类型（必录）
备注:
1.示例Model数据包中字段顺序不建议改变，否则可能会有相互影响，如果出现字段值被覆盖或丢失，则可以尝试把字段顺序向后调整一下。
2.示例Model数据包默认包含允许引入的字段，实际按需构建既可。
3.如需创建关联关系，可参考http://club.kingdee.com/forum.php?mod=viewthread&tid=1394265 。

## 二、返回结果：

{"Result":{"ResponseStatus":{"ErrorCode":"","IsSuccess":"false","Errors":[{"FieldName":"","Message":"","DIndex":0}],"SuccessEntitys":[{"Id":"","Number":"","DIndex":0}],"SuccessMessages":[{"FieldName":"","Message":"","DIndex":0}],"MsgCode":""},"Id":"","Number":"","NeedReturnData":[{}]}}

## 三、代码示例：

// 使用webapi引用组件Kingdee.BOS.WebApi.Client.dll
K3CloudApiClient client = new K3CloudApiClient("http://192.168.0.43/k3cloud/"); 
var loginResult = client.ValidateLogin("62e096c858caf7","Administrator","888888",2052);
var resultType = JObject.Parse(loginResult)["LoginResultType"].Value<int>();
//登录结果类型等于1，代表登录成功
if (resultType == 1)
{
	 client.Save("BD_MATERIAL","{"NeedUpDateFields":[],"NeedReturnFields":[],"IsDeleteEntry":"true","SubSystemId":"","IsVerifyBaseDataField":"false","IsEntryBatchFill":"true","ValidateFlag":"true","NumberSearch":"true","IsAutoAdjustField":"false","InterationFlags":"","IgnoreInterationFlag":"","Model":{"FMATERIALID":0,"FCreateOrgId":{"FNumber":""},"FUseOrgId":{"FNumber":""},"FNumber":"","FName":"","FSpecification":"","FMnemonicCode":"","FOldNumber":"","FDescription":"","FMaterialGroup":{"FNumber":""},"FDSMatchByLot":"false","FImgStorageType":"","FIsSalseByNet":"false","FPrdNameEn":"","FOVERTIME":0,"FTCMD":0,"FTCHD":0,"FISYTM":"false","FForbidReson":"","FExtVar":"","F_ZZYCL":{"FNumber":""},"F_SYKA_Text":"","F_SYKA_Text1":"","F_SYKA_SqOrgId":{"FNumber":""},"F_SYKA_Shareware":"false","F_BHS_ApplyOrgId":{"FNumber":""},"F_BHS_Shareware":"false","F_SYKA_Assistant":{"FNumber":""},"F_BHS_Cooperate":"false","F_SYKA_BaseDataText":{"FNUMBER":""},"F_SYKA_1212":"","FSubHeadEntity":{"FEntryId":0,"FIsControlSal":"false","FLowerPercent":0,"FUpPercent":0,"FCalculateBase":"","FMaxSalPrice_CMK":0,"FMinSalPrice_CMK":0,"FIsAutoRemove":"false","FIsMailVirtual":"false","FIsFreeSend":"","FTimeUnit":"","FRentFreeDura":0,"FPricingStep":0,"FMinRentDura":0,"FRentBeginPrice":0,"FPriceType":"","FRentStepPrice":0,"FDepositAmount":0,"FLogisticsCount":0,"FRequestMinPackQty":0,"FMinRequestQty":0,"FRetailUnitID":{"FNUMBER":""},"FIsPrinttAg":"false","FIsAccessory":"false"},"SubHeadEntity":{"FEntryId":0,"FBARCODE":"","FErpClsID":"","FFeatureItem":"","FCONFIGTYPE":"","FCategoryID":{"FNumber":""},"FTaxType":{"FNumber":""},"FTaxRateId":{"FNUMBER":""},"FBaseUnitId":{"FNumber":""},"FIsPurchase":"false","FIsInventory":"false","FIsSubContract":"false","FIsSale":"false","FIsProduce":"false","FIsAsset":"false","FGROSSWEIGHT":0,"FNETWEIGHT":0,"FWEIGHTUNITID":{"FNUMBER":""},"FWholeHeight":0,"FLENGTH":0,"FWIDTH":0,"FHEIGHT":0,"FVOLUME":0,"FVOLUMEUNITID":{"FNUMBER":""},"FSuite":"","FCostPriceRate":0,"FUseOrgId1":{"FNumber":""}},"SubHeadEntity1":{"FEntryId":0,"FStoreUnitID":{"FNumber":""},"FAuxUnitID":{"FNumber":""},"FUnitConvertDir":"","FStockId":{"FNumber":""},"FStockPlaceId":{"FSTOCKPLACEID__FF101003":{"FNumber":""},"FSTOCKPLACEID__FF100001":{"FNumber":""},"FSTOCKPLACEID__FF115001":{"FNumber":""}},"FIsLockStock":"false","FIsCycleCounting":"false","FCountCycle":"","FCountDay":0,"FIsMustCounting":"false","FIsBatchManage":"false","FBatchRuleID":{"FNumber":""},"FIsKFPeriod":"false","FIsExpParToFlot":"false","FExpUnit":"","FExpPeriod":0,"FOnlineLife":0,"FRefCost":0,"FCurrencyId":{"FNumber":""},"FIsEnableMinStock":"false","FIsEnableMaxStock":"false","FIsEnableSafeStock":"false","FIsEnableReOrder":"false","FMinStock":0,"FSafeStock":0,"FReOrderGood":0,"FEconReOrderQty":0,"FMaxStock":0,"FIsSNManage":"false","FIsSNPRDTracy":"false","FSNCodeRule":{"FNumber":""},"FSNUnit":{"FNumber":""},"FSNManageType":"","FSNGenerateTime":"","FBoxStandardQty":0,"FUseOrgId2":{"FNumber":""}},"SubHeadEntity2":{"FEntryId":0,"FSaleUnitId":{"FNumber":""},"FSalePriceUnitId":{"FNumber":""},"FOrderQty":0,"FMinQty":0,"FMaxQty":0,"FOutStockLmtH":0,"FOutStockLmtL":0,"FAgentSalReduceRate":0,"FIsATPCheck":"false","FIsReturnPart":"false","FIsInvoice":"false","FIsReturn":"false","FAllowPublish":"false","FISAFTERSALE":"false","FISPRODUCTFILES":"false","FISWARRANTED":"false","FWARRANTY":0,"FWARRANTYUNITID":"","FOutLmtUnit":"","FTaxCategoryCodeId":{"FNUMBER":""},"FSalGroup":{"FNumber":""},"FIsTaxEnjoy":"false","FTaxDiscountsType":"","FUseOrgId3":{"FNumber":""},"FUnValidateExpQty":"false"},"SubHeadEntity3":{"FEntryId":0,"FBaseMinSplitQty":0,"FPurchaseUnitId":{"FNumber":""},"FPurchasePriceUnitId":{"FNumber":""},"FPurchaseOrgId":{"FNumber":""},"FPurchaseGroupId":{"FNumber":""},"FPurchaserId":{"FNumber":""},"FDefaultVendor":{"FNumber":""},"FChargeID":{"FNumber":""},"FIsQuota":"false","FQuotaType":"","FMinSplitQty":0,"FIsVmiBusiness":"false","FEnableSL":"false","FIsPR":"false","FIsReturnMaterial":"false","FIsSourceControl":"false","FReceiveMaxScale":0,"FReceiveMinScale":0,"FReceiveAdvanceDays":0,"FReceiveDelayDays":0,"FPOBillTypeId":{"FNUMBER":""},"FAgentPurPlusRate":0,"FDefBarCodeRuleId":{"FNUMBER":""},"FPrintCount":0,"FMinPackCount":0,"FUseOrgId4":{"FNumber":""}},"SubHeadEntity4":{"FEntryId":0,"FPlanMode":"","FBaseVarLeadTimeLotSize":0,"FPlanningStrategy":"","FMfgPolicyId":{"FNumber":""},"FOrderPolicy":"","FPlanWorkshop":{"FNumber":""},"FFixLeadTime":0,"FFixLeadTimeType":"","FVarLeadTime":0,"FVarLeadTimeType":"","FCheckLeadTime":0,"FCheckLeadTimeType":"","FOrderIntervalTimeType":"","FOrderIntervalTime":0,"FMaxPOQty":0,"FMinPOQty":0,"FIncreaseQty":0,"FEOQ":0,"FVarLeadTimeLotSize":0,"FPlanIntervalsDays":0,"FPlanBatchSplitQty":0,"FRequestTimeZone":0,"FPlanTimeZone":0,"FPlanGroupId":{"FNumber":""},"FATOSchemeId":{"FNUMBER":""},"FPlanerID":{"FNumber":""},"FIsMrpComBill":"false","FCanLeadDays":0,"FIsMrpComReq":"false","FLeadExtendDay":0,"FReserveType":"","FPlanSafeStockQty":0,"FAllowPartAhead":"false","FCanDelayDays":0,"FDelayExtendDay":0,"FAllowPartDelay":"false","FPlanOffsetTimeType":"","FPlanOffsetTime":0,"FSupplySourceId":{"FNumber":""},"FTimeFactorId":{"FNumber":""},"FQtyFactorId":{"FNumber":""},"FProductLine":{"FNUMBER":""},"FWriteOffQty":0,"FPlanIdent":{"FNumber":""},"FProScheTrackId":{"FNumber":""},"FDailyOutQty":0,"FUseOrgId7":{"FNumber":""}},"SubHeadEntity5":{"FEntryId":0,"FWorkShopId":{"FNumber":""},"FProduceUnitId":{"FNumber":""},"FFinishReceiptOverRate":0,"FFinishReceiptShortRate":0,"FProduceBillType":{"FNUMBER":""},"FOrgTrustBillType":{"FNUMBER":""},"FIsSNCarryToParent":"false","FIsProductLine":"false","FBOMUnitId":{"FNumber":""},"FLOSSPERCENT":0,"FConsumVolatility":0,"FIsMainPrd":"false","FIsCoby":"false","FIsECN":"false","FIssueType":"","FBKFLTime":"","FPickStockId":{"FNumber":""},"FPickBinId":{"FPICKBINID__FF101003":{"FNumber":""},"FPICKBINID__FF100001":{"FNumber":""},"FPICKBINID__FF115001":{"FNumber":""}},"FOverControlMode":"","FMinIssueQty":0,"FISMinIssueQty":"false","FIsKitting":"false","FIsCompleteSet":"false","FDefaultRouting":{"FNumber":""},"FStdLaborPrePareTime":0,"FStdLaborProcessTime":0,"FStdMachinePrepareTime":0,"FStdMachineProcessTime":0,"FMinIssueUnitId":{"FNUMBER":""},"FMdlId":{"FNUMBER":""},"FMdlMaterialId":{"FNUMBER":""},"FStandHourUnitId":"","FBackFlushType":"","FFIXLOSS":0,"FUseOrgId6":{"FNumber":""},"FIsEnableSchedule":"false","FDefaultLineId":{"FNUMBER":""}},"SubHeadEntity7":{"FEntryId":0,"FSubconUnitId":{"FNumber":""},"FSubconPriceUnitId":{"FNumber":""},"FSubBillType":{"FNUMBER":""},"FUseOrgId8":{"FNumber":""}},"SubHeadEntity6":{"FEntryId":0,"FCheckIncoming":"false","FCheckProduct":"false","FCheckStock":"false","FCheckReturn":"false","FCheckDelivery":"false","FEnableCyclistQCSTK":"false","FStockCycle":0,"FEnableCyclistQCSTKEW":"false","FEWLeadDay":0,"FIncSampSchemeId":{"FNUMBER":""},"FIncQcSchemeId":{"FNUMBER":""},"FInspectGroupId":{"FNUMBER":""},"FInspectorId":{"FNUMBER":""},"FCheckEntrusted":"false","FCheckOther":"false","FIsFirstInspect":"false","FUseOrgId5":{"FNumber":""},"FCheckReturnMtrl":"false"},"FBarCodeEntity_CMK":[{"FEntryID":0,"FCodeType_CMK":"","FUnitId_CMK":{"FNUMBER":""}}],"FSpecialAttributeEntity":[{"FEntryID":0}],"FEntityAuxPty":[{"FEntryID":0,"FAuxPropertyId":{"FNumber":""},"FIsEnable1":"false","FIsComControl":"false","FIsAffectPrice1":"false","FIsAffectPlan1":"false","FIsAffectCost1":"false","FIsMustInput":"false","FUseOrgId11":{"FNumber":""},"FValueType":""}],"FEntityInvPty":[{"FEntryID":0,"FUseOrgId10":{"FNumber":""},"FInvPtyId":{"FNumber":""},"FIsEnable":"false","FIsAffectPrice":"false","FIsAffectPlan":"false","FIsAffectCost":"false"}]}}");
 }

## 四、JSON格式数据：

{
    "NeedUpDateFields": [],
    "NeedReturnFields": [],
    "IsDeleteEntry": "true",
    "SubSystemId": "",
    "IsVerifyBaseDataField": "false",
    "IsEntryBatchFill": "true",
    "ValidateFlag": "true",
    "NumberSearch": "true",
    "IsAutoAdjustField": "false",
    "InterationFlags": "",
    "IgnoreInterationFlag": "",
    "Model": {
        "FMATERIALID": 0,
        "FCreateOrgId": {
            "FNumber": ""
        },
        "FUseOrgId": {
            "FNumber": ""
        },
        "FNumber": "",
        "FName": "",
        "FSpecification": "",
        "FMnemonicCode": "",
        "FOldNumber": "",
        "FDescription": "",
        "FMaterialGroup": {
            "FNumber": ""
        },
        "FDSMatchByLot": "false",
        "FImgStorageType": "",
        "FIsSalseByNet": "false",
        "FPrdNameEn": "",
        "FOVERTIME": 0,
        "FTCMD": 0,
        "FTCHD": 0,
        "FISYTM": "false",
        "FForbidReson": "",
        "FExtVar": "",
        "F_ZZYCL": {
            "FNumber": ""
        },
        "F_SYKA_Text": "",
        "F_SYKA_Text1": "",
        "F_SYKA_SqOrgId": {
            "FNumber": ""
        },
        "F_SYKA_Shareware": "false",
        "F_BHS_ApplyOrgId": {
            "FNumber": ""
        },
        "F_BHS_Shareware": "false",
        "F_SYKA_Assistant": {
            "FNumber": ""
        },
        "F_BHS_Cooperate": "false",
        "F_SYKA_BaseDataText": {
            "FNUMBER": ""
        },
        "F_SYKA_1212": "",
        "FSubHeadEntity": {
            "FEntryId": 0,
            "FIsControlSal": "false",
            "FLowerPercent": 0,
            "FUpPercent": 0,
            "FCalculateBase": "",
            "FMaxSalPrice_CMK": 0,
            "FMinSalPrice_CMK": 0,
            "FIsAutoRemove": "false",
            "FIsMailVirtual": "false",
            "FIsFreeSend": "",
            "FTimeUnit": "",
            "FRentFreeDura": 0,
            "FPricingStep": 0,
            "FMinRentDura": 0,
            "FRentBeginPrice": 0,
            "FPriceType": "",
            "FRentStepPrice": 0,
            "FDepositAmount": 0,
            "FLogisticsCount": 0,
            "FRequestMinPackQty": 0,
            "FMinRequestQty": 0,
            "FRetailUnitID": {
                "FNUMBER": ""
            },
            "FIsPrinttAg": "false",
            "FIsAccessory": "false"
        },
        "SubHeadEntity": {
            "FEntryId": 0,
            "FBARCODE": "",
            "FErpClsID": "",
            "FFeatureItem": "",
            "FCONFIGTYPE": "",
            "FCategoryID": {
                "FNumber": ""
            },
            "FTaxType": {
                "FNumber": ""
            },
            "FTaxRateId": {
                "FNUMBER": ""
            },
            "FBaseUnitId": {
                "FNumber": ""
            },
            "FIsPurchase": "false",
            "FIsInventory": "false",
            "FIsSubContract": "false",
            "FIsSale": "false",
            "FIsProduce": "false",
            "FIsAsset": "false",
            "FGROSSWEIGHT": 0,
            "FNETWEIGHT": 0,
            "FWEIGHTUNITID": {
                "FNUMBER": ""
            },
            "FWholeHeight": 0,
            "FLENGTH": 0,
            "FWIDTH": 0,
            "FHEIGHT": 0,
            "FVOLUME": 0,
            "FVOLUMEUNITID": {
                "FNUMBER": ""
            },
            "FSuite": "",
            "FCostPriceRate": 0,
            "FUseOrgId1": {
                "FNumber": ""
            }
        },
        "SubHeadEntity1": {
            "FEntryId": 0,
            "FStoreUnitID": {
                "FNumber": ""
            },
            "FAuxUnitID": {
                "FNumber": ""
            },
            "FUnitConvertDir": "",
            "FStockId": {
                "FNumber": ""
            },
            "FStockPlaceId": {
                "FSTOCKPLACEID__FF101003": {
                    "FNumber": ""
                },
                "FSTOCKPLACEID__FF100001": {
                    "FNumber": ""
                },
                "FSTOCKPLACEID__FF115001": {
                    "FNumber": ""
                }
            },
            "FIsLockStock": "false",
            "FIsCycleCounting": "false",
            "FCountCycle": "",
            "FCountDay": 0,
            "FIsMustCounting": "false",
            "FIsBatchManage": "false",
            "FBatchRuleID": {
                "FNumber": ""
            },
            "FIsKFPeriod": "false",
            "FIsExpParToFlot": "false",
            "FExpUnit": "",
            "FExpPeriod": 0,
            "FOnlineLife": 0,
            "FRefCost": 0,
            "FCurrencyId": {
                "FNumber": ""
            },
            "FIsEnableMinStock": "false",
            "FIsEnableMaxStock": "false",
            "FIsEnableSafeStock": "false",
            "FIsEnableReOrder": "false",
            "FMinStock": 0,
            "FSafeStock": 0,
            "FReOrderGood": 0,
            "FEconReOrderQty": 0,
            "FMaxStock": 0,
            "FIsSNManage": "false",
            "FIsSNPRDTracy": "false",
            "FSNCodeRule": {
                "FNumber": ""
            },
            "FSNUnit": {
                "FNumber": ""
            },
            "FSNManageType": "",
            "FSNGenerateTime": "",
            "FBoxStandardQty": 0,
            "FUseOrgId2": {
                "FNumber": ""
            }
        },
        "SubHeadEntity2": {
            "FEntryId": 0,
            "FSaleUnitId": {
                "FNumber": ""
            },
            "FSalePriceUnitId": {
                "FNumber": ""
            },
            "FOrderQty": 0,
            "FMinQty": 0,
            "FMaxQty": 0,
            "FOutStockLmtH": 0,
            "FOutStockLmtL": 0,
            "FAgentSalReduceRate": 0,
            "FIsATPCheck": "false",
            "FIsReturnPart": "false",
            "FIsInvoice": "false",
            "FIsReturn": "false",
            "FAllowPublish": "false",
            "FISAFTERSALE": "false",
            "FISPRODUCTFILES": "false",
            "FISWARRANTED": "false",
            "FWARRANTY": 0,
            "FWARRANTYUNITID": "",
            "FOutLmtUnit": "",
            "FTaxCategoryCodeId": {
                "FNUMBER": ""
            },
            "FSalGroup": {
                "FNumber": ""
            },
            "FIsTaxEnjoy": "false",
            "FTaxDiscountsType": "",
            "FUseOrgId3": {
                "FNumber": ""
            },
            "FUnValidateExpQty": "false"
        },
        "SubHeadEntity3": {
            "FEntryId": 0,
            "FBaseMinSplitQty": 0,
            "FPurchaseUnitId": {
                "FNumber": ""
            },
            "FPurchasePriceUnitId": {
                "FNumber": ""
            },
            "FPurchaseOrgId": {
                "FNumber": ""
            },
            "FPurchaseGroupId": {
                "FNumber": ""
            },
            "FPurchaserId": {
                "FNumber": ""
            },
            "FDefaultVendor": {
                "FNumber": ""
            },
            "FChargeID": {
                "FNumber": ""
            },
            "FIsQuota": "false",
            "FQuotaType": "",
            "FMinSplitQty": 0,
            "FIsVmiBusiness": "false",
            "FEnableSL": "false",
            "FIsPR": "false",
            "FIsReturnMaterial": "false",
            "FIsSourceControl": "false",
            "FReceiveMaxScale": 0,
            "FReceiveMinScale": 0,
            "FReceiveAdvanceDays": 0,
            "FReceiveDelayDays": 0,
            "FPOBillTypeId": {
                "FNUMBER": ""
            },
            "FAgentPurPlusRate": 0,
            "FDefBarCodeRuleId": {
                "FNUMBER": ""
            },
            "FPrintCount": 0,
            "FMinPackCount": 0,
            "FUseOrgId4": {
                "FNumber": ""
            }
        },
        "SubHeadEntity4": {
            "FEntryId": 0,
            "FPlanMode": "",
            "FBaseVarLeadTimeLotSize": 0,
            "FPlanningStrategy": "",
            "FMfgPolicyId": {
                "FNumber": ""
            },
            "FOrderPolicy": "",
            "FPlanWorkshop": {
                "FNumber": ""
            },
            "FFixLeadTime": 0,
            "FFixLeadTimeType": "",
            "FVarLeadTime": 0,
            "FVarLeadTimeType": "",
            "FCheckLeadTime": 0,
            "FCheckLeadTimeType": "",
            "FOrderIntervalTimeType": "",
            "FOrderIntervalTime": 0,
            "FMaxPOQty": 0,
            "FMinPOQty": 0,
            "FIncreaseQty": 0,
            "FEOQ": 0,
            "FVarLeadTimeLotSize": 0,
            "FPlanIntervalsDays": 0,
            "FPlanBatchSplitQty": 0,
            "FRequestTimeZone": 0,
            "FPlanTimeZone": 0,
            "FPlanGroupId": {
                "FNumber": ""
            },
            "FATOSchemeId": {
                "FNUMBER": ""
            },
            "FPlanerID": {
                "FNumber": ""
            },
            "FIsMrpComBill": "false",
            "FCanLeadDays": 0,
            "FIsMrpComReq": "false",
            "FLeadExtendDay": 0,
            "FReserveType": "",
            "FPlanSafeStockQty": 0,
            "FAllowPartAhead": "false",
            "FCanDelayDays": 0,
            "FDelayExtendDay": 0,
            "FAllowPartDelay": "false",
            "FPlanOffsetTimeType": "",
            "FPlanOffsetTime": 0,
            "FSupplySourceId": {
                "FNumber": ""
            },
            "FTimeFactorId": {
                "FNumber": ""
            },
            "FQtyFactorId": {
                "FNumber": ""
            },
            "FProductLine": {
                "FNUMBER": ""
            },
            "FWriteOffQty": 0,
            "FPlanIdent": {
                "FNumber": ""
            },
            "FProScheTrackId": {
                "FNumber": ""
            },
            "FDailyOutQty": 0,
            "FUseOrgId7": {
                "FNumber": ""
            }
        },
        "SubHeadEntity5": {
            "FEntryId": 0,
            "FWorkShopId": {
                "FNumber": ""
            },
            "FProduceUnitId": {
                "FNumber": ""
            },
            "FFinishReceiptOverRate": 0,
            "FFinishReceiptShortRate": 0,
            "FProduceBillType": {
                "FNUMBER": ""
            },
            "FOrgTrustBillType": {
                "FNUMBER": ""
            },
            "FIsSNCarryToParent": "false",
            "FIsProductLine": "false",
            "FBOMUnitId": {
                "FNumber": ""
            },
            "FLOSSPERCENT": 0,
            "FConsumVolatility": 0,
            "FIsMainPrd": "false",
            "FIsCoby": "false",
            "FIsECN": "false",
            "FIssueType": "",
            "FBKFLTime": "",
            "FPickStockId": {
                "FNumber": ""
            },
            "FPickBinId": {
                "FPICKBINID__FF101003": {
                    "FNumber": ""
                },
                "FPICKBINID__FF100001": {
                    "FNumber": ""
                },
                "FPICKBINID__FF115001": {
                    "FNumber": ""
                }
            },
            "FOverControlMode": "",
            "FMinIssueQty": 0,
            "FISMinIssueQty": "false",
            "FIsKitting": "false",
            "FIsCompleteSet": "false",
            "FDefaultRouting": {
                "FNumber": ""
            },
            "FStdLaborPrePareTime": 0,
            "FStdLaborProcessTime": 0,
            "FStdMachinePrepareTime": 0,
            "FStdMachineProcessTime": 0,
            "FMinIssueUnitId": {
                "FNUMBER": ""
            },
            "FMdlId": {
                "FNUMBER": ""
            },
            "FMdlMaterialId": {
                "FNUMBER": ""
            },
            "FStandHourUnitId": "",
            "FBackFlushType": "",
            "FFIXLOSS": 0,
            "FUseOrgId6": {
                "FNumber": ""
            },
            "FIsEnableSchedule": "false",
            "FDefaultLineId": {
                "FNUMBER": ""
            }
        },
        "SubHeadEntity7": {
            "FEntryId": 0,
            "FSubconUnitId": {
                "FNumber": ""
            },
            "FSubconPriceUnitId": {
                "FNumber": ""
            },
            "FSubBillType": {
                "FNUMBER": ""
            },
            "FUseOrgId8": {
                "FNumber": ""
            }
        },
        "SubHeadEntity6": {
            "FEntryId": 0,
            "FCheckIncoming": "false",
            "FCheckProduct": "false",
            "FCheckStock": "false",
            "FCheckReturn": "false",
            "FCheckDelivery": "false",
            "FEnableCyclistQCSTK": "false",
            "FStockCycle": 0,
            "FEnableCyclistQCSTKEW": "false",
            "FEWLeadDay": 0,
            "FIncSampSchemeId": {
                "FNUMBER": ""
            },
            "FIncQcSchemeId": {
                "FNUMBER": ""
            },
            "FInspectGroupId": {
                "FNUMBER": ""
            },
            "FInspectorId": {
                "FNUMBER": ""
            },
            "FCheckEntrusted": "false",
            "FCheckOther": "false",
            "FIsFirstInspect": "false",
            "FUseOrgId5": {
                "FNumber": ""
            },
            "FCheckReturnMtrl": "false"
        },
        "FBarCodeEntity_CMK": [
            {
                "FEntryID": 0,
                "FCodeType_CMK": "",
                "FUnitId_CMK": {
                    "FNUMBER": ""
                }
            }
        ],
        "FSpecialAttributeEntity": [
            {
                "FEntryID": 0
            }
        ],
        "FEntityAuxPty": [
            {
                "FEntryID": 0,
                "FAuxPropertyId": {
                    "FNumber": ""
                },
                "FIsEnable1": "false",
                "FIsComControl": "false",
                "FIsAffectPrice1": "false",
                "FIsAffectPlan1": "false",
                "FIsAffectCost1": "false",
                "FIsMustInput": "false",
                "FUseOrgId11": {
                    "FNumber": ""
                },
                "FValueType": ""
            }
        ],
        "FEntityInvPty": [
            {
                "FEntryID": 0,
                "FUseOrgId10": {
                    "FNumber": ""
                },
                "FInvPtyId": {
                    "FNumber": ""
                },
                "FIsEnable": "false",
                "FIsAffectPrice": "false",
                "FIsAffectPlan": "false",
                "FIsAffectCost": "false"
            }
        ]
    }
}

## 五、字段说明：

物料：FBillHead 
	 实体主键：FMATERIALID 
	 数据状态：FDocumentStatus 
	 禁用状态：FForbidStatus 
	 名称：FName  (必填项)
	 编码：FNumber 
	 描述：FDescription 
	 创建组织：FCreateOrgId  (必填项)
	 使用组织：FUseOrgId  (必填项)
	 创建人：FCreatorId 
	 修改人：FModifierId 
	 创建日期：FCreateDate 
	 修改日期：FModifyDate 
	 助记码：FMnemonicCode 
	 规格型号：FSpecification 
	 禁用人：FForbidderId 
	 禁用日期：FForbidDate 
	 审核日期：FApproveDate 
	 审核人：FApproverId 
	 图片(数据库)：FImage1 
	 旧物料编码：FOldNumber 
	 物料分组：FMaterialGroup  (必填项)
	 物料分组编码：FBaseProperty 
	 PLM物料内码：FPLMMaterialId 
	 物料来源：FMaterialSRC 
	 校验模式：FIsValidate 
	 图片(文件服务器)：FImageFileServer 
	 图片存储类型：FImgStorageType 
	 图片(数据库)：FIsImgDataBase 
	 图片(文件服务)：FIsImgFileServer 
	 是否网销：FIsSalseByNet 
	 自动分配：FIsAutoAllocate 
	 SPU信息：FSPUID 
	 拼音：FPinYin 
	 按批号匹配供需：FDSMatchByLot 
	 禁用原因：FForbidReson 
	 已使用：FRefStatus 
	 扩展编码：FExtVar 
	 英文名：FPrdNameEn 
	 超时小时：FOVERTIME 
	 涂层密度：FTCMD 
	 涂层厚度：FTCHD 
	 是否预涂膜：FISYTM 
	 总账原材料名称：F_ZZYCL 
	 辊面材质：F_SYKA_Text 
	 区域：F_SYKA_Text1 
	 申请组织：F_SYKA_SqOrgId  (必填项)
	 共享配件：F_SYKA_Shareware 
	 申请组织：F_BHS_ApplyOrgId 
	 共享配件：F_BHS_Shareware 
	 采购类别：F_SYKA_Assistant 
	 订单协同：F_BHS_Cooperate 
	 可编辑基础资料：F_SYKA_BaseDataText 
	 文本：F_SYKA_1212 
     零售特性：FSubHeadEntity 
	 实体主键：FEntryId 
	 商品类型：FComTypeId_CMK 
	 条码前缀：FBarCodeHeader_CMK 
	 主条形码：FGoodBarCode_CMK 
	 品牌：FComBrandId_CMK 
	 经营方式：FBusinessType_CMK 
	 售卖方式：FSellMethod_CMK 
	 本位币：FCurrencyId_CMK 
	 零售状态：FSaleStatus_CMK 
	 采购状态：FPurStatus_CMK 
	 采购单价：FPurPrice_CMK 
	 零售价：FSalePrice_CMK 
	 会员价：FVIPPrice_CMK 
	 积分比率：FPointsRate_CMK 
	 图片(文件服务器)：FImgFile_CMK 
	 所属专柜：FShoppeID_CMK 
	 门店供货价：FLSProPrice 
	 商品来源：FMaterialSource 
	 是否按区间价控制销售：FIsControlSal 
	 上调百分比：FUpPercent 
	 下调百分比：FLowerPercent 
	 计算基数：FCalculateBase 
	 最高销售价：FMaxSalPrice_CMK 
	 最低销售价：FMinSalPrice_CMK 
	 组装商品退货是否自动拆卸：FIsAutoRemove 
	 快递费虚拟商品：FIsMailVirtual 
	 是否包邮：FIsFreeSend 
	 包邮：FPackageMail 
	 运费模板：FFreightTem 
	 物流计价方式：FPriceType 
	 按件：FPerUnit 
	 按体积：FByVolume 
	 按重量：FByWeight 
	 物流计量：FLogisticsCount 
	 零售最小包装量：FRequestMinPackQty 
	 门店最低要货量：FMinRequestQty 
	 零售单位：FRetailUnitID 
	 是否打印：FIsPrinttAg 
	 是否辅料：FIsAccessory 
	 计时单位：FTimeUnit 
	 最低租赁时长：FMinRentDura 
	 租赁起步价：FRentBeginPrice 
	 计价步长：FPricingStep 
	 租赁步长单价：FRentStepPrice 
	 免租时长：FRentFreeDura 
	 押金金额：FDepositAmount 
     条形码：FBarCodeEntity_CMK 
	 实体主键：FEntryID 
	 条形码类型：FCodeType_CMK  (必填项)
	 条形码：FBarCode_CMK 
	 计量单位：FUnitId_CMK  (必填项)
	 会员卡级别：FVIPCardLevel_CMK 
	 零售价：FPrice_CMK 
	 备注：FRemarks_CMK 
	 会员价：FVIPPrice 
	 门店供货价：FProPrice 
     规格属性列表：FSpecialAttributeEntity 
	 实体主键：FEntryID 
	 规格属性类别：FSpecAttrCategoryID 
	 规格属性：FSpecialAttributeID 
     基本：SubHeadEntity 
	 实体主键：FEntryId 
	 物料属性：FErpClsID  (必填项)
	 允许库存：FIsInventory 
	 允许销售：FIsSale 
	 允许资产：FIsAsset 
	 允许委外：FIsSubContract 
	 允许生产：FIsProduce 
	 允许采购：FIsPurchase 
	 即时核算：FIsRealTimeAccout 
	 基本单位：FBaseUnitId  (必填项)
	 税分类：FTaxType 
	 物料分类：FTypeID 
	 发出商品：FF101003
	 高新产品组：FF100001
	 配件中心：FF115001
	 存货类别：FCategoryID  (必填项)
	 默认税率：FTaxRateId 
	 条码：FBARCODE 
	 重量单位：FWEIGHTUNITID 
	 尺寸单位：FVOLUMEUNITID 
	 毛重：FGROSSWEIGHT 
	 净重：FNETWEIGHT 
	 长：FLENGTH 
	 密度：FVOLUME 
	 宽：FWIDTH 
	 厚度：FHEIGHT 
	 配置生产：FCONFIGTYPE 
	 套件：FSuite  (必填项)
	 结算成本价加减价比例(%)：FCostPriceRate 
	 英文名称：FNameEn 
	 制式：FSysModel 
	 颜色：FColor 
	 传播名：FSpreadName 
	 开票方：FMAKEINVOICEPARTY 
	 特征件子项：FFeatureItem  (必填项)
	 使用组织：FUseOrgId1 
	 成品厚度：FWholeHeight 
     库存：SubHeadEntity1 
	 实体主键：FEntryId 
	 库存单位：FStoreUnitID  (必填项)
	 辅助单位：FAuxUnitID 
	 仓库：FStockId 
	 可锁库：FIsLockStock 
	 批号编码规则：FBatchRuleID 
	 保质期单位：FExpUnit 
	 仓位：FStockPlaceId 
	 发出商品：FF101003
	 高新产品组：FF100001
	 配件中心：FF115001
	 在架寿命期：FOnlineLife 
	 保质期：FExpPeriod 
	 库存单位换算率分子：FStoreURNum 
	 库存单位换算率分母：FStoreURNom 
	 启用批号管理：FIsBatchManage 
	 启用保质期管理：FIsKFPeriod 
	 批号附属信息：FIsExpParToFlot 
	 启用盘点周期：FIsCycleCounting 
	 必盘：FIsMustCounting 
	 币别：FCurrencyId  (必填项)
	 参考成本：FRefCost 
	 盘点周期单位：FCountCycle 
	 盘点周期：FCountDay 
	 库存管理：FIsSNManage 
	 序列号编码规则：FSNCodeRule 
	 序列号单位：FSNUnit 
	 安全库存：FSafeStock 
	 再订货点：FReOrderGood 
	 最小库存：FMinStock 
	 最大库存：FMaxStock 
	 换算方向：FUnitConvertDir  (必填项)
	 启用最小库存：FIsEnableMinStock 
	 启用安全库存：FIsEnableSafeStock 
	 启用最大库存：FIsEnableMaxStock 
	 启用再订货点：FIsEnableReOrder 
	 经济订货批量：FEconReOrderQty 
	 生产追溯：FIsSNPRDTracy 
	 序列号生成时机：FSNGenerateTime  (必填项)
	 业务范围：FSNManageType  (必填项)
	 单箱标准数量：FBoxStandardQty 
	 使用组织：FUseOrgId2 
     销售：SubHeadEntity2 
	 实体主键：FEntryId 
	 ATP检查：FIsATPCheck 
	 销售计价单位：FSalePriceUnitId  (必填项)
	 销售单位：FSaleUnitId  (必填项)
	 可开票：FIsInvoice 
	 最大批量：FMaxQty 
	 允许退货：FIsReturn 
	 最小批量：FMinQty 
	 部件可退：FIsReturnPart 
	 起订量：FOrderQty 
	 销售计价单位换算率分母：FSalePriceURNom 
	 销售单位换算率分子：FSaleURNum 
	 销售计价单位换算率分子：FSalePriceURNum 
	 销售单位换算率分母：FSaleURNom 
	 超发上限(%)：FOutStockLmtH 
	 超发下限(%)：FOutStockLmtL 
	 代理销售减价比例(%)：FAgentSalReduceRate 
	 允许发布到订货平台：FAllowPublish 
	 启用售后服务：FISAFTERSALE 
	 生成产品档案：FISPRODUCTFILES 
	 是否保修：FISWARRANTED 
	 保修期：FWARRANTY 
	 保修期单位：FWARRANTYUNITID 
	 超发控制单位：FOutLmtUnit 
	 税收分类编码：FTaxCategoryCodeId 
	 销售分组：FSalGroup 
	 税收优惠政策类型：FTaxDiscountsType 
	 享受税收优惠政策：FIsTaxEnjoy 
	 使用组织：FUseOrgId3 
	 不参与可发量统计：FUnValidateExpQty 
     采购：SubHeadEntity3 
	 实体主键：FEntryId 
	 采购单位：FPurchaseUnitId  (必填项)
	 采购员：FPurchaserId 
	 默认供应商：FDefaultVendor 
	 货源控制：FIsSourceControl 
	 需要请购：FIsPR 
	 收货下限比例(%)：FReceiveMinScale 
	 采购组：FPurchaseGroupId 
	 收货上限比例(%)：FReceiveMaxScale 
	 采购计价单位：FPurchasePriceUnitId  (必填项)
	 供应商资质：FIsVendorQualification 
	 收货提前天数：FReceiveAdvanceDays 
	 收货延迟天数：FReceiveDelayDays 
	 采购单位换算率分子：FPurURNum 
	 采购计价单位换算率分子：FPurPriceURNum 
	 采购单位换算率分母：FPurURNom 
	 采购计价单位换算率分母：FPurPriceURNom 
	 配额管理：FIsQuota 
	 配额方式：FQuotaType  (必填项)
	 代理采购加成比例：FAgentPurPlusRate 
	 费用项目：FChargeID 
	 最小拆分数量：FMinSplitQty 
	 基本单位最小拆分数量：FBaseMinSplitQty 
	 VMI业务：FIsVmiBusiness 
	 允许退料：FIsReturnMaterial 
	 启用商联在线(6.1弃用)：FEnableSL 
	 采购组织：FPurchaseOrgId 
	 默认条码规则：FDefBarCodeRuleId 
	 重复打印数：FPrintCount 
	 采购类型：FPOBillTypeId 
	 最小包装数：FMinPackCount 
	 使用组织：FUseOrgId4 
    计划：SubHeadEntity4 
	 实体主键：FEntryId 
	 计划员：FPlanerID 
	 固定/经济批量：FEOQ 
	 计划策略：FPlanningStrategy  (必填项)
	 订货策略：FOrderPolicy  (必填项)
	 计划区：FPlanWorkshop 
	 固定提前期单位：FFixLeadTimeType  (必填项)
	 固定提前期：FFixLeadTime 
	 变动提前期单位：FVarLeadTimeType  (必填项)
	 变动提前期：FVarLeadTime 
	 检验提前期单位：FCheckLeadTimeType  (必填项)
	 检验提前期：FCheckLeadTime 
	 订货间隔期单位：FOrderIntervalTimeType  (必填项)
	 订货间隔期：FOrderIntervalTime 
	 批量拆分间隔天数：FPlanIntervalsDays 
	 拆分批量：FPlanBatchSplitQty   
	 计划时界：FPlanTimeZone 
	 需求时界：FRequestTimeZone 
	 MRP计算是否合并需求：FIsMrpComReq 
	 预留类型：FReserveType  (必填项)
	 允许提前天数：FCanLeadDays 
	 提前宽限期：FLeadExtendDay 
	 延后宽限期：FDelayExtendDay 
	 允许延后天数：FCanDelayDays 
	 时间单位：FPlanOffsetTimeType  (必填项)
	 偏置时间：FPlanOffsetTime 
	 最小订货量：FMinPOQty 
	 最小包装量：FIncreaseQty 
	 最大订货量：FMaxPOQty 
	 变动提前期批量：FVarLeadTimeLotSize 
	 基本变动提前期批量：FBaseVarLeadTimeLotSize 
	 计划组：FPlanGroupId 
	 制造策略：FMfgPolicyId 
	 供应来源：FSupplySourceId 
	 时间紧迫系数：FTimeFactorId 
	 数量负荷系数：FQtyFactorId 
	 计划模式：FPlanMode 
	 预计入库允许部分延后：FAllowPartDelay 
	 预计入库允许部分提前：FAllowPartAhead 
	 安全库存：FPlanSafeStockQty 
	 ATO预测冲销方案：FATOSchemeId 
	 累计提前期：FAccuLeadTime 
	 产品系列：FProductLine 
	 冲销数量：FWriteOffQty 
	 计划标识：FPlanIdent 
	 订单进度分组：FProScheTrackId 
	 日产量：FDailyOutQty 
	 MRP计算是否按单合并：FIsMrpComBill 
	 使用组织：FUseOrgId7 
     生产：SubHeadEntity5 
	 实体主键：FEntryId 
	 发料仓库：FPickStockId 
	 子项单位：FBOMUnitId 
	 生产车间：FWorkShopId 
	 发料方式：FIssueType  (必填项)
	 生产单位：FProduceUnitId 
	 是否关键件：FIsKitting 
	 默认工艺路线：FDefaultRouting 
	 可为联副产品：FIsCoby 
	 标准工时：FPerUnitStandHour 
	 倒冲时机：FBKFLTime 
	 入库超收比例(%)：FFinishReceiptOverRate 
	 入库欠收比例(%)：FFinishReceiptShortRate 
	 发料仓位：FPickBinId 
	 发出商品：FF101003
	 高新产品组：FF100001
	 配件中心：FF115001
	 生产单位换算率分子：FPrdURNum 
	 生产单位换算率分母：FPrdURNom 
	 BOM单位换算率分子：FBOMURNum 
	 BOM单位换算率分母：FBOMURNom 
	 可为主产品：FIsMainPrd 
	 是否齐套件：FIsCompleteSet 
	 超发控制方式：FOverControlMode  (必填项)
	 最小发料批量：FMinIssueQty 
	 标准人员准备工时：FStdLaborPrePareTime 
	 标准人员实作工时：FStdLaborProcessTime 
	 标准机器准备工时：FStdMachinePrepareTime 
	 标准机器实作工时：FStdMachineProcessTime 
	 消耗波动(%)：FConsumVolatility 
	 生产线生产：FIsProductLine 
	 生产类型：FProduceBillType 
	 组织间受托类型：FOrgTrustBillType 
	 领料考虑最小发料批量：FISMinIssueQty 
	 启用ECN：FIsECN 
	 最小发料批量单位：FMinIssueUnitId  (必填项)
	 产品模型：FMdlId 
	 模型物料：FMdlMaterialId 
	 变动损耗率(%)：FLOSSPERCENT 
	 序列号携带到父项：FIsSNCarryToParent 
	 工时单位：FStandHourUnitId  (必填项)
	 倒冲数量：FBackFlushType  (必填项)
	 固定损耗：FFIXLOSS 
	 使用组织：FUseOrgId6 
	 启用日排产：FIsEnableSchedule 
	 默认产线：FDefaultLineId 
     辅助属性：FEntityAuxPty 
	 实体主键：FEntryID 
	 启用：FIsEnable1 
	 必录：FIsMustInput 
	 影响价格：FIsAffectPrice1 
	 影响计划：FIsAffectPlan1 
	 影响出库成本：FIsAffectCost1 
	 组合控制：FIsComControl 
	 辅助属性：FAuxPropertyId 
	 取值方式：FValueType 
	 值设置状态：FValueSet 
	 使用组织：FUseOrgId11 
     库存属性：FEntityInvPty 
	 实体主键：FEntryID 
	 启用：FIsEnable 
	 影响价格：FIsAffectPrice 
	 影响计划：FIsAffectPlan 
	 影响出库成本：FIsAffectCost 
	 库存属性：FInvPtyId  (必填项)
	 使用组织：FUseOrgId10 
     委外：SubHeadEntity7 
	 实体主键：FEntryId 
	 委外单位：FSubconUnitId 
	 委外计价单位：FSubconPriceUnitId 
	 委外单位换算率分子：FSUBCONURNUM 
	 委外单位换算率分母：FSUBCONURNOM 
	 委外计价单位换算率分子：FSUBCONPRICEURNUM 
	 委外计价单位换算率分母：FSUBCONPRICEURNOM 
	 委外类型：FSubBillType 
	 使用组织：FUseOrgId8 
     质量：SubHeadEntity6 
	 实体主键：FEntryId 
	 产品检验：FCheckProduct 
	 来料检验：FCheckIncoming 
	 抽样方案：FIncSampSchemeId 
	 质检方案：FIncQcSchemeId 
	 库存检验：FCheckStock 
	 启用库存周期复检：FEnableCyclistQCSTK 
	 启用库存周期复检提醒：FEnableCyclistQCSTKEW 
	 提醒提前期：FEWLeadDay 
	 复检周期：FStockCycle 
	 发货检验：FCheckDelivery 
	 退货检验：FCheckReturn 
	 质检组：FInspectGroupId 
	 质检员：FInspectorId 
	 受托材料检验：FCheckEntrusted 
	 其他检验：FCheckOther 
	 产品首检：FIsFirstInspect 
	 使用组织：FUseOrgId5 
	 生产退料检验：FCheckReturnMtrl 

## 备注：错误代码MsgCode说明

​           0：默认
​           1：上下文丢失
​           2：没有权限
​           3：操作标识为空
​           4：异常
​           5：单据标识为空
​           6：数据库操作失败
​           7：许可错误
​           8：参数错误
​           9：指定字段/值不存在
​           10：未找到对应数据
​           11：验证失败
​           12：不可操作
​           13：网控冲突

