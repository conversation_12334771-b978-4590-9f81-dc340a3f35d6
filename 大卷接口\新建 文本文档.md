一、请求参数说明：
1.formid：业务对象表单Id，字符串类型（必录）
2.data：JSON格式数据（详情参考JSON格式数据）（必录）
     2.1.FieldKeys：需查询的字段key集合，字符串类型，格式："key1,key2,..."（必录）
     2.2.SchemeId：过滤方案内码，字符串类型
     2.3.StartRow：开始行索引，整型（非必录）
     2.4.Limit：最大行数，整型，不能超过2000（非必录）
     2.5.Model：表单数据包，JSON类型（必录）

二、返回结果：
{"Result": {"IsSuccess": true,"RowCount": 0,"Rows": [ ] }}

三、代码示例：
// 使用webapi引用组件Kingdee.BOS.WebApi.Client.dll
K3CloudApiClient client = new K3CloudApiClient("http://192.168.0.43/k3cloud/"); 
var loginResult = client.ValidateLogin("62e096c858caf7","Administrator","888888",2052);
var resultType = JObject.Parse(loginResult)["LoginResultType"].Value<int>();
//登录结果类型等于1，代表登录成功
if (resultType == 1)
{
	 client.GetSysReportData("k19f7e027a411471baf336064f04f738f","{"FieldKeys":"","SchemeId":"","StartRow":0,"Limit":0,"Model":{"F_VLQX_Isstore":"false"}}");
 }

四、JSON格式数据：
{
    "FieldKeys": "",
    "SchemeId": "",
    "StartRow": 0,
    "Limit": 0,
    "Model": {
        "F_VLQX_Isstore": "false"
    }
}

五、字段说明：
	 组织：F_BHS_Organization 
	 物料编码：F_BHS_FmaterialId 
	 仓库：F_BHS_FstockId 
	 预订单联系人：F_PAEZ_Assistant 
	 是否库存：F_VLQX_Isstore 

参数FieldKeys显示列：
	 预订单联系人：F_PAEZ_Text 
	 部门：F_PAEZ_Text1 
	 单据日期：F_PAEZ_Date2 
	 摘要(单据类型)：F_PAEZ_Text2 
	 单据编号：F_PAEZ_Text3 
	 数量：F_PAEZ_Qty 
	 单价：F_PAEZ_Price 
	 金额：F_PAEZ_Amount 
	 余额：F_PAEZ_Amount1 
	 结算方式：F_PAEZ_Text4 
	 业务员：F_PAEZ_Text5 
	 客户类型：F_PAEZ_Text6 

备注：错误代码MsgCode说明
           0：默认
           1：上下文丢失
           2：没有权限
           3：操作标识为空
           4：异常
           5：单据标识为空
           6：数据库操作失败
           7：许可错误
           8：参数错误
           9：指定字段/值不存在
           10：未找到对应数据
           11：验证失败
           12：不可操作
           13：网控冲突