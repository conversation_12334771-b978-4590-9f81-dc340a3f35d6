
CREATE TABLE public.cr_client (
	client_id text DEFAULT af_auid() NOT NULL,
	client_status text DEFAULT '110'::text NOT NULL,
	client_no text DEFAULT af_ss_no_generate('client_no'::character varying) NOT NULL,
	client_name text NOT NULL,
	client_name_py text NULL,
	client_name_abbr text NOT NULL,
	client_name_abbr_py text NULL,
	client_country text NULL,
	client_province text NULL,
	client_city text NULL,
	client_address text NULL,
	client_postcode text NULL,
	client_tel text NULL,
	client_fax text NULL,
	client_email text NULL,
	client_website text NULL,
	client_priority int4 DEFAULT 1 NOT NULL,
	client_owner_user_id text NULL,
	client_payment text NULL,
	client_settle text NULL,
	client_trad_currency text NULL,
	client_discount numeric(18, 3) DEFAULT 0 NOT NULL,
	client_trans_days int2 DEFAULT 1 NOT NULL,
	client_owner_last text NULL,
	mrp_region_no text NULL,
	mrp_type text DEFAULT 'SFSO'::character varying NOT NULL,
	doc_keyword text NULL,
	location_id text DEFAULT '1' NOT NULL,
	cost_punish_delay_times numeric(18, 3) DEFAULT 1 NOT NULL,
	cost_punish_ahead_times numeric(18, 3) DEFAULT 1 NOT NULL,
	cost_punish_similar_times numeric(18, 3) DEFAULT 1 NOT NULL,
	client_type1 text NULL,
	client_type2 text NULL,
	client_type3 text NULL,
	client_type4 text NULL,
	client_type5 text DEFAULT '1' NOT NULL,
	client_type6 text NULL,
	client_type7 text NULL,
	client_type8 text NULL,
	client_type9 text NULL,
	client_type10 text NULL,
	client_rmk01 text NULL,
	client_rmk02 text NULL,
	client_rmk03 text NULL,
	client_rmk04 text NULL,
	client_rmk05 text NULL,
	client_rmk06 text NULL,
	client_rmk07 text NULL,
	client_rmk08 text NULL,
	client_rmk09 text NULL,
	client_rmk10 text NULL,
	client_rmk11 text NULL,
	client_rmk12 text NULL,
	client_rmk13 text NULL,
	client_rmk14 text NULL,
	client_rmk15 text NULL,
	client_rmk16 text NULL,
	client_rmk17 text NULL,
	client_rmk18 text NULL,
	client_rmk19 text NULL,
	crt_time timestamp NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	client_invp_rule_no bpchar(1) DEFAULT '1'::bpchar NOT NULL,
	trasprortation_mode_no text DEFAULT 'car'::text NOT NULL,
	CONSTRAINT cr_client_check CHECK ((client_status = ANY (ARRAY['100'::text, '110'::text, '120'::text, '130'::text]))),
	CONSTRAINT cr_client_check_1 CHECK ((mrp_type = ANY (ARRAY['SO'::text, 'SF'::text, 'SFSO'::text]))),
	CONSTRAINT cr_client_check_2 CHECK ((client_invp_rule_no = ANY (ARRAY['1'::bpchar, '2'::bpchar]))),
	CONSTRAINT pk_cr_client PRIMARY KEY (client_id),
	CONSTRAINT unique_client_name UNIQUE (client_name),
	CONSTRAINT unique_client_name_abbr UNIQUE (client_name_abbr),
	CONSTRAINT unique_client_no UNIQUE (client_no),
	CONSTRAINT cr_client_fk FOREIGN KEY (location_id) REFERENCES public.tr_location(location_id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT cr_client_fk2 FOREIGN KEY (trasprortation_mode_no) REFERENCES public.tr_trasprortation_mode(trasprortation_mode_no) ON DELETE RESTRICT ON UPDATE CASCADE
)