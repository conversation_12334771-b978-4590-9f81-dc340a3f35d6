
```javascript
//定义返回数据数组
var datalist=[];
//主函数
var TopRowCount=0;//设置每次程序执行抓取的总数，0为全部
function main(){


    var StartRow=0;//设置初始开始查询位置
    var Limit=5000;//设置递归每页抓取条数
    //设置查询哪些字段
    var FieldKeys_str=ss_io_tbl.ss_io_tblf.fieldSourceStr;
    var FieldKeys=FieldKeys_str.split(',');
    getDataList(StartRow,Limit,FieldKeys_str);
    Log.LogInfo("抓取到"+datalist.length+"条记录");//写日志
    var res_data=[];
    for(var i=0;i<datalist.length;i++){
        var item={};
        for(var k=0;k<FieldKeys.length;k++){
            item[FieldKeys[k]]=datalist[i][k];

        }
        res_data.push(item);
    }

     const result =res_data.filter((item, index, self) => {
        return self.findIndex(t => t.FNumber=== item.FNumber) === index;
    });

    


    Log.LogInfo("去重后还剩"+result.length+"条记录");//写日志
    return result;
}
//递归函数分页拉取金蝶数据
function getDataList(StartRow,Limit,FieldKeys){
     Log.LogInfo(ss_io_tbl.ss_io_tbl_ext.io_last_data_time);
    //配置post_form参数
    var parameters={
            data:JSON.stringify({
                    FormId: "BD_MATERIAL",
                    FieldKeys: FieldKeys,
                    FilterString:"FApproveDate >= '"+ss_io_tbl.ss_io_tbl_ext.io_last_data_time+"' and  FForbidStatus = 'A' and FUseOrgId.FNumber = '100' ",
                    OrderString: "",
                    TopRowCount: TopRowCount,
                    StartRow: StartRow,
                    Limit: Limit,
                    SubSystemId: ""
            })
    };
    //获取授权动作中返回的cookie
    var cookies=ss_io_tbl.io_read_auth.ext.cookie;
    var handler={Cookie:cookies};//
    var response = HttpApi.post(
        ss_io_tbl.io_read_auth.io_auth_db_host+"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc",
        {form_params:parameters},
        handler
    );
     response=response.Content.ReadAsStringAsync().Result;
    var result=JSON.parse(response);
    
    if(result["FSrcBillNo"]!=null && result["FSrcBillNo"]["Result"]["ResponseStatus"]["Errors"].length>0){
        throw response;
    }
    if(result[0]!=null && result[0][0]!=null && result[0][0]["Result"]!=null){
        throw response;
    }
    
    //将每次的数据与datalist合并   
    if(result.length>0){
      datalist=datalist.concat(result);
    } 
    
    if(result.length>=Limit){
        StartRow=StartRow+Limit;
        getDataList(StartRow,Limit,FieldKeys);
    }
}

