```java
header.AddChild("guige", new LocaleValue("规格型号", this.Context.UserLocale.LCID));
header.AddChild("fLengh", new LocaleValue("长度", this.Context.UserLocale.LCID));
var sl = header.AddChild("sl", new LocaleValue("数量"), SqlStorageType.SqlDecimal);
//header.AddChild("pcs", new LocaleValue("件数", this.Context.UserLocale.LCID));
var pcs = header.AddChild("pcs", new LocaleValue("件数"), SqlStorageType.SqlDecimal);
header.AddChild("qx", new LocaleValue("缺陷", this.Context.UserLocale.LCID));
header.AddChild("remark", new LocaleValue("备注", this.Context.UserLocale.LCID));
header.AddChild("fnotetext", new LocaleValue("订单备注", this.Context.UserLocale.LCID));
//var billNo = header.AddChild("DJBH", new LocaleValue("单据编号"));
//header.AddChild("sl", new LocaleValue("数量", SqlStorageType.SqlDecimal));
header.AddChild("days", new LocaleValue("天数", this.Context.UserLocale.LCID)); 
header.AddChild("barcodeStatus", new LocaleValue("出库状态", this.Context.UserLocale.LCID));