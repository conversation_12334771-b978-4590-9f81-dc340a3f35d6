CREATE OR REPLACE FUNCTION "public"."af_ps_jumboroll_inv_tmp_after"()
  RETURNS "pg_catalog"."varchar" AS $BODY$
	--  导入后，写入PCM主表           
declare jsonDatas json;
res returntype; 
begin
    
		INSERT INTO ps_jumboroll_inv("company_no", "jumboroll_no", "part_no", "part_idt", "part_length", "part_width", "part_height", "part_margin_left", "part_margin_right", "mrp_region_no", "client_no", "lot_no", "invp_no", "valid_date_from", "valid_date_to", "jumboroll_user_defined_cost", "unit_price", "inv_first_in_date", "inv_last_out_date", "ps_jumboroll_inv_rmk01", "ps_jumboroll_inv_rmk02", "ps_jumboroll_inv_rmk03", "ps_jumboroll_inv_rmk04", "ps_jumboroll_inv_rmk05", "ps_jumboroll_inv_rmk06", "crt_time", "crt_user", "crt_user_no", "crt_user_name", "crt_host", "upd_time", "upd_user", "upd_user_no", "upd_user_name", "upd_host")
		
		select "company_no", "jumboroll_no", "part_no", "part_idt", "part_length", "part_width", "part_height", "part_margin_left", "part_margin_right",  "mrp_region_no", "client_no", "lot_no", "invp_no", "valid_date_from", "valid_date_to", "jumboroll_user_defined_cost", "unit_price", "inv_first_in_date", "inv_last_out_date", "ps_jumboroll_inv_rmk01", "ps_jumboroll_inv_rmk02", "ps_jumboroll_inv_rmk03", "ps_jumboroll_inv_rmk04", "ps_jumboroll_inv_rmk05", "ps_jumboroll_inv_rmk06"
		, "crt_time",'sys' as crt_user, 'sys' as crt_user_no, 'sys' as crt_user_name,'sys' as crt_host,
		 "upd_time", 'sys' as upd_user,'sys' as upd_user_no, 'sys' as upd_user_name,'sys' as upd_hostc
		 from ps_jumboroll_inv_tmp
		 where 1=1
		 	 on conflict (part_no) do update 
			 set part_length=excluded.part_length,
			 part_idt=excluded.part_idt,
       part_no=excluded.part_no,
			 ps_jumboroll_inv_rmk01=excluded.ps_jumboroll_inv_rmk01;
		
		
		   
       truncate ps_jumboroll_inv_tmp;
		


   res:=row('true','成功!');
   return to_json(res);
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100